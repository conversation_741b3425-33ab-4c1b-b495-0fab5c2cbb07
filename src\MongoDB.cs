using MongoDB.Driver;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Microsoft.Extensions.Configuration;
using static Scraper.Program;
using static Scraper.Utilities;

namespace Scraper
{
    public partial class MongoDB
    {
        // MongoDB singletons
        public static MongoClient? mongoClient;
        public static IMongoDatabase? database;
        public static IMongoCollection<Product>? mongoCollection;

        // EstablishConnection()
        // ---------------------
        // Establishes a connection using settings defined in appsettings.json.

        public static async Task<bool> EstablishConnection(
            string dbName,
            string collectionName
        )
        {
            try
            {
                // Read from appsettings.json or appsettings.local.json
                string connectionString = config!.GetRequiredSection("MONGODB_CONNECTION_STRING").Get<string>()!;

                mongoClient = new MongoClient(connectionString);
                database = mongoClient.GetDatabase(dbName);
                mongoCollection = database.GetCollection<Product>(collectionName);

                // Test the connection
                await database.RunCommandAsync((Command<BsonDocument>)"{ping:1}");

                Log($"\n(Connected to MongoDB) {mongoClient.Settings.Server}", ConsoleColor.Yellow);
                return true;
            }
            catch (MongoException e)
            {
                LogError(e.GetType().ToString());
                LogError(
                    "Error Connecting to MongoDB - check appsettings.json, " +
                    "connection string may be invalid"
                );
                return false;
            }
            catch (HttpRequestException e)
            {
                LogError(e.GetType().ToString());
                LogError(
                    "Error Connecting to MongoDB - check firewall and internet status"
                );
                return false;
            }
            catch (Exception e)
            {
                LogError(e.GetType().ToString());
                LogError(
                    "Error Connecting to MongoDB - make sure appsettings.json " +
                    "is created and contains:"
                );
                Log(
                    "{\n" +
                    "\t\"MONGODB_CONNECTION_STRING\": \"mongodb://localhost:27017\"\n" +
                    "}\n"
                );
                return false;
            }
        }

        // UpsertProduct()
        // ---------------
        // Takes a scraped Product, and tries to insert it or update it on MongoDB.

        public async static Task<UpsertResponse> UpsertProduct(Product scrapedProduct)
        {
            try
            {
                // Check if product already exists in MongoDB
                var filter = Builders<Product>.Filter.Eq(p => p.id, scrapedProduct.id);
                var existingProduct = await mongoCollection!.Find(filter).FirstOrDefaultAsync();

                if (existingProduct != null)
                {
                    // Build an updated product with values from both the DB and scraped products
                    ProductResponse productResponse = BuildUpdatedProduct(existingProduct, scrapedProduct);

                    // Replace the existing product in MongoDB
                    await mongoCollection!.ReplaceOneAsync(filter, productResponse.product);

                    // Return the UpsertResponse based on what data has changed
                    return productResponse.upsertResponse;
                }
                else
                {
                    // No existing product was found, insert new product
                    return await InsertNewProduct(scrapedProduct);
                }
            }
            catch (MongoException e)
            {
                LogError($"MongoDB Error: {e.Message}");
                return UpsertResponse.Failed;
            }
            catch (Exception e)
            {
                LogError(e.GetType().ToString());
                Log(e.ToString());
            }

            // Return failed if this part is ever reached
            return UpsertResponse.Failed;
        }

        // BuildUpdatedProduct()
        // --------------------
        // Builds a product with combined data from scrapedProduct, and price history data from dbProduct.

        public static ProductResponse BuildUpdatedProduct(Product dbProduct, Product scrapedProduct)
        {
            // Measure the price difference between the new scraped product and the old db product
            float priceDifference = Math.Abs(dbProduct.currentPrice - scrapedProduct.currentPrice);

            // Check if price has changed by more than $0.05
            bool priceHasChanged = priceDifference > 0.05;

            // Check if DB product has category set
            string oldCategories;
            try
            {
                oldCategories = string.Join(" ", dbProduct.category);
            }
            catch
            {
                oldCategories = string.Empty;
            }

            string newCategories = string.Join(" ", scrapedProduct.category);

            // Check if size, categories, or other minor values have changed
            bool otherDataHasChanged =
                dbProduct!.size != scrapedProduct.size ||
                oldCategories != newCategories ||
                dbProduct.sourceSite != scrapedProduct.sourceSite ||
                dbProduct.name != scrapedProduct.name ||
                dbProduct.unitPrice != scrapedProduct.unitPrice ||
                dbProduct.unitName != scrapedProduct.unitName ||
                dbProduct.originalUnitQuantity != scrapedProduct.originalUnitQuantity
            ;

            // If price has changed and not on the same day, we can do a full update from the scraped product
            if (priceHasChanged &&
                dbProduct.lastUpdated.ToShortDateString() !=
                scrapedProduct.lastUpdated.ToShortDateString()
            )
            {
                // Price has changed, so we can create an updated Product with the changes
                List<DatedPrice> updatedHistory = dbProduct.priceHistory.ToList<DatedPrice>();
                updatedHistory.Add(scrapedProduct.priceHistory[0]);

                // Log price change with different verb and colour depending on price change direction
                bool priceTrendingDown = scrapedProduct.currentPrice < dbProduct!.currentPrice;
                string priceTrendText = "  Price " + (priceTrendingDown ? "Down " : "Up   ") + ":";

                Log(
                    $"{priceTrendText} {dbProduct.name.PadRight(51).Substring(0, 51)} | " +
                    $"${dbProduct.currentPrice} > ${scrapedProduct.currentPrice}",
                    priceTrendingDown ? ConsoleColor.Green : ConsoleColor.Red
                );

                // Return new product with updated data
                return new ProductResponse(UpsertResponse.PriceUpdated, new Product(
                    dbProduct.id,
                    scrapedProduct.name,
                    scrapedProduct.size,
                    scrapedProduct.currentPrice,
                    scrapedProduct.category,
                    scrapedProduct.sourceSite,
                    updatedHistory.ToArray(),
                    scrapedProduct.lastUpdated,
                    scrapedProduct.lastChecked,
                    scrapedProduct.unitPrice,
                    scrapedProduct.unitName,
                    scrapedProduct.originalUnitQuantity
                ));
            }
            else if (otherDataHasChanged)
            {
                // If only non-price data has changed, update non price/date fields
                return new ProductResponse(UpsertResponse.NonPriceUpdated, new Product(
                    dbProduct.id,
                    scrapedProduct.name,
                    scrapedProduct.size,
                    dbProduct.currentPrice,
                    scrapedProduct.category,
                    scrapedProduct.sourceSite,
                    dbProduct.priceHistory,
                    dbProduct.lastUpdated,
                    scrapedProduct.lastChecked,
                    scrapedProduct.unitPrice,
                    scrapedProduct.unitName,
                    scrapedProduct.originalUnitQuantity
                ));
            }
            else
            {
                // Else existing DB Product has not changed, update only lastChecked
                return new ProductResponse(UpsertResponse.AlreadyUpToDate, new Product(
                    dbProduct.id,
                    dbProduct.name,
                    dbProduct.size,
                    dbProduct.currentPrice,
                    dbProduct.category,
                    dbProduct.sourceSite,
                    dbProduct.priceHistory,
                    dbProduct.lastUpdated,
                    scrapedProduct.lastChecked,
                    dbProduct.unitPrice,
                    dbProduct.unitName,
                    dbProduct.originalUnitQuantity
                ));
            }
        }

        public enum UpsertResponse
        {
            NewProduct,
            PriceUpdated,
            NonPriceUpdated,
            AlreadyUpToDate,
            Failed
        }

        public struct ProductResponse
        {
            public UpsertResponse upsertResponse;
            public Product product;

            public ProductResponse(UpsertResponse upsertResponse, Product product) : this()
            {
                this.upsertResponse = upsertResponse;
                this.product = product;
            }
        }

        // InsertNewProduct()
        // ------------------
        // Inserts a new Product into MongoDB

        private static async Task<UpsertResponse> InsertNewProduct(Product scrapedProduct)
        {
            try
            {
                // No existing product was found, insert into MongoDB
                await mongoCollection!.InsertOneAsync(scrapedProduct);

                Log(
                    $"  New Product: {scrapedProduct.id,-8} | " +
                    $"{scrapedProduct.name!.PadRight(40).Substring(0, 40)}" +
                    $" | $ {scrapedProduct.currentPrice,5} | {scrapedProduct.size}"
                );

                return UpsertResponse.NewProduct;
            }
            catch (MongoException e)
            {
                Log($"  MongoDB: Insert Error for new Product: {e.Message}");
                return UpsertResponse.Failed;
            }
        }

        // CustomQuery()
        // -------------
        // Is used for debugging using MongoDB queries

        public static async Task CustomQuery()
        {
            // Example: Delete all products from 'newworld' source site
            var filter = Builders<Product>.Filter.Regex(p => p.sourceSite, "newworld");
            var products = await mongoCollection!.Find(filter).ToListAsync();

            foreach (var item in products)
            {
                Log($"  Deleting {item.id} - {item.name}");
                await mongoCollection.DeleteOneAsync(Builders<Product>.Filter.Eq(p => p.id, item.id));
            }
        }
    }
}